using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Implementation of optimized staging schema service
/// Phase 1.1.1: Enhanced Staging Architecture with Proper Indexes
/// Performance target: Sub-millisecond FK lookups, 5,000-10,000 records per batch
/// </summary>
public class StagingSchemaService : IStagingSchemaService
{
    private readonly ILogger<StagingSchemaService> _logger;
    private readonly BulkSeederConfiguration _options;
    private readonly IEnvironmentConfigurationService _environmentService;
    private static readonly object _schemaInitLock = new();
    private static bool _schemaInitialized = false;

    public StagingSchemaService(
        ILogger<StagingSchemaService> logger,
        IOptions<BulkSeederConfiguration> options,
        IEnvironmentConfigurationService environmentService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
    }

    public async Task InitializeOptimizedStagingSchemaAsync(CancellationToken cancellationToken = default)
    {
        if (_schemaInitialized)
        {
            _logger.LogDebug("Staging schema already initialized, skipping");
            return;
        }

        lock (_schemaInitLock)
        {
            if (_schemaInitialized)
                return;

            _logger.LogInformation("Initializing optimized staging schema with enhanced indexes...");
        }

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            // Note: Staging schema creation has been disabled in favor of direct insertion approach
            // The original staging schema script has been removed as it's no longer needed
            _logger.LogInformation("Staging schema creation skipped - using direct insertion approach");

            result.Success = true;
            result.Duration = DateTime.UtcNow - startTime;
            result.Summary = "Staging schema creation skipped - direct insertion approach is used";
            result.TablesCreated = 0; // No staging tables created

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Staging schema service error (staging disabled)");
            result.Success = false;
            result.Duration = DateTime.UtcNow - startTime;
            result.Summary = $"Staging schema service error: {ex.Message}";
            return result;
        }
    }

    public async Task<Guid> CreateEnhancedSeederSessionAsync(
        string sessionName,
        string environment,
        string createdBy,
        string? configurationSnapshot = null,
        CancellationToken cancellationToken = default)
    {
        await InitializeOptimizedStagingSchemaAsync(cancellationToken);

        var sessionId = Guid.NewGuid();

        _logger.LogInformation("Creating enhanced seeder session {SessionId} with name '{SessionName}' in environment {Environment}",
            sessionId, sessionName, environment);

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            const string sql = @"
                INSERT INTO [Staging].[SeederSession] (
                    [Id], [SessionName], [StartTime], [Status], [Environment], [CreatedBy],
                    [ConfigurationSnapshot], [DataSourceInfo]
                )
                VALUES (
                    @SessionId, @SessionName, @StartTime, @Status, @Environment, @CreatedBy,
                    @ConfigurationSnapshot, @DataSourceInfo
                )";

            var dataSourceInfo = JsonSerializer.Serialize(new
            {
                ConnectionString = _environmentService.CurrentMigrationConfiguration.DatabaseConnection?.Substring(0, 50) + "...",
                Environment = environment,
                MachineName = Environment.MachineName,
                UserDomainName = Environment.UserDomainName,
                ProcessorCount = Environment.ProcessorCount,
                WorkingSet = Environment.WorkingSet
            });

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@SessionId", sessionId);
            command.Parameters.AddWithValue("@SessionName", sessionName);
            command.Parameters.AddWithValue("@StartTime", DateTime.UtcNow);
            command.Parameters.AddWithValue("@Status", "Created");
            command.Parameters.AddWithValue("@Environment", environment);
            command.Parameters.AddWithValue("@CreatedBy", createdBy);
            command.Parameters.AddWithValue("@ConfigurationSnapshot", configurationSnapshot ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@DataSourceInfo", dataSourceInfo);

            await command.ExecuteNonQueryAsync(cancellationToken);

            _logger.LogInformation("Enhanced seeder session {SessionId} created successfully", sessionId);
            return sessionId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create enhanced seeder session {SessionId}", sessionId);
            throw;
        }
    }

    public async Task UpdateSessionProgressAsync(
        Guid sessionId,
        SessionProgressUpdate progressUpdate,
        CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            const string sql = @"
                UPDATE [Staging].[SeederSession] 
                SET 
                    [Status] = @Status,
                    [CurrentOperation] = @CurrentOperation,
                    [ProgressPercentage] = @ProgressPercentage,
                    [EstimatedCompletionTime] = @EstimatedCompletionTime,
                    [ThroughputRecordsPerSecond] = @ThroughputRecordsPerSecond,
                    [ProcessedDrivers] = ISNULL(@ProcessedDrivers, [ProcessedDrivers]),
                    [ProcessedVehicles] = ISNULL(@ProcessedVehicles, [ProcessedVehicles]),
                    [ProcessedCards] = ISNULL(@ProcessedCards, [ProcessedCards]),
                    [ProcessedAccessRecords] = ISNULL(@ProcessedAccessRecords, [ProcessedAccessRecords]),
                    [ApiCallsTotal] = ISNULL(@ApiCallsTotal, [ApiCallsTotal]),
                    [ApiCallsSuccessful] = ISNULL(@ApiCallsSuccessful, [ApiCallsSuccessful]),
                    [ApiCallsFailed] = ISNULL(@ApiCallsFailed, [ApiCallsFailed]),
                    [ApiResponseTimeMs] = ISNULL(@ApiResponseTimeMs, [ApiResponseTimeMs]),
                    [SqlOperationsTotal] = ISNULL(@SqlOperationsTotal, [SqlOperationsTotal]),
                    [SqlOperationsSuccessful] = ISNULL(@SqlOperationsSuccessful, [SqlOperationsSuccessful]),
                    [SqlOperationsFailed] = ISNULL(@SqlOperationsFailed, [SqlOperationsFailed]),
                    [BulkInsertTimeMs] = ISNULL(@BulkInsertTimeMs, [BulkInsertTimeMs]),
                    [ValidationTimeMs] = ISNULL(@ValidationTimeMs, [ValidationTimeMs]),
                    [ProcessingTimeMs] = ISNULL(@ProcessingTimeMs, [ProcessingTimeMs]),
                    [ValidationErrors] = ISNULL(@ValidationErrors, [ValidationErrors]),
                    [BusinessRuleViolations] = ISNULL(@BusinessRuleViolations, [BusinessRuleViolations]),
                    [ReferentialIntegrityErrors] = ISNULL(@ReferentialIntegrityErrors, [ReferentialIntegrityErrors]),
                    [PeakMemoryUsageMB] = ISNULL(@PeakMemoryUsageMB, [PeakMemoryUsageMB]),
                    [CacheHitRatio] = ISNULL(@CacheHitRatio, [CacheHitRatio]),
                    [ConnectionPoolUsage] = ISNULL(@ConnectionPoolUsage, [ConnectionPoolUsage]),
                    [ErrorLog] = ISNULL(@ErrorLog, [ErrorLog]),
                    [WarningsLog] = ISNULL(@WarningsLog, [WarningsLog]),
                    [EndTime] = CASE WHEN @Status IN ('Completed', 'Failed', 'Cancelled') THEN GETUTCDATE() ELSE [EndTime] END
                WHERE [Id] = @SessionId";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@SessionId", sessionId);
            command.Parameters.AddWithValue("@Status", progressUpdate.Status);
            command.Parameters.AddWithValue("@CurrentOperation", progressUpdate.CurrentOperation ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ProgressPercentage", progressUpdate.ProgressPercentage);
            command.Parameters.AddWithValue("@EstimatedCompletionTime", progressUpdate.EstimatedCompletionTime ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ThroughputRecordsPerSecond", progressUpdate.ThroughputRecordsPerSecond ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ProcessedDrivers", progressUpdate.ProcessedDrivers ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ProcessedVehicles", progressUpdate.ProcessedVehicles ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ProcessedCards", progressUpdate.ProcessedCards ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ProcessedAccessRecords", progressUpdate.ProcessedAccessRecords ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ApiCallsTotal", progressUpdate.ApiCallsTotal ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ApiCallsSuccessful", progressUpdate.ApiCallsSuccessful ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ApiCallsFailed", progressUpdate.ApiCallsFailed ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ApiResponseTimeMs", progressUpdate.ApiResponseTimeMs ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@SqlOperationsTotal", progressUpdate.SqlOperationsTotal ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@SqlOperationsSuccessful", progressUpdate.SqlOperationsSuccessful ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@SqlOperationsFailed", progressUpdate.SqlOperationsFailed ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@BulkInsertTimeMs", progressUpdate.BulkInsertTimeMs ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ValidationTimeMs", progressUpdate.ValidationTimeMs ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ProcessingTimeMs", progressUpdate.ProcessingTimeMs ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ValidationErrors", progressUpdate.ValidationErrors ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@BusinessRuleViolations", progressUpdate.BusinessRuleViolations ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ReferentialIntegrityErrors", progressUpdate.ReferentialIntegrityErrors ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@PeakMemoryUsageMB", progressUpdate.PeakMemoryUsageMB ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@CacheHitRatio", progressUpdate.CacheHitRatio ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ConnectionPoolUsage", progressUpdate.ConnectionPoolUsage ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@ErrorLog", progressUpdate.ErrorLog ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@WarningsLog", progressUpdate.WarningsLog ?? (object)DBNull.Value);

            await command.ExecuteNonQueryAsync(cancellationToken);

            _logger.LogDebug("Session {SessionId} progress updated: {Status} ({Progress}%)",
                sessionId, progressUpdate.Status, progressUpdate.ProgressPercentage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update session progress for {SessionId}", sessionId);
            throw;
        }
    }

    public async Task<SchemaValidationResult> ValidateStagingSchemaAsync(CancellationToken cancellationToken = default)
    {
        var result = new SchemaValidationResult();

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            // Check for required tables and indexes
            const string validationSql = @"
                -- Check required tables exist
                SELECT 
                    'SeederSession' as TableName,
                    CASE WHEN OBJECT_ID('[Staging].[SeederSession]') IS NOT NULL THEN 1 ELSE 0 END as Exists
                UNION ALL
                SELECT 'DriverStaging', CASE WHEN OBJECT_ID('[Staging].[DriverStaging]') IS NOT NULL THEN 1 ELSE 0 END
                UNION ALL
                SELECT 'VehicleStaging', CASE WHEN OBJECT_ID('[Staging].[VehicleStaging]') IS NOT NULL THEN 1 ELSE 0 END
                UNION ALL
                SELECT 'CardStaging', CASE WHEN OBJECT_ID('[Staging].[CardStaging]') IS NOT NULL THEN 1 ELSE 0 END
                UNION ALL
                SELECT 'AccessPermissionStaging', CASE WHEN OBJECT_ID('[Staging].[AccessPermissionStaging]') IS NOT NULL THEN 1 ELSE 0 END
                UNION ALL
                SELECT 'CustomerCache', CASE WHEN OBJECT_ID('[Staging].[CustomerCache]') IS NOT NULL THEN 1 ELSE 0 END
                UNION ALL
                SELECT 'SiteCache', CASE WHEN OBJECT_ID('[Staging].[SiteCache]') IS NOT NULL THEN 1 ELSE 0 END
                UNION ALL
                SELECT 'DepartmentCache', CASE WHEN OBJECT_ID('[Staging].[DepartmentCache]') IS NOT NULL THEN 1 ELSE 0 END
                UNION ALL
                SELECT 'ModelCache', CASE WHEN OBJECT_ID('[Staging].[ModelCache]') IS NOT NULL THEN 1 ELSE 0 END
                UNION ALL
                SELECT 'ModuleCache', CASE WHEN OBJECT_ID('[Staging].[ModuleCache]') IS NOT NULL THEN 1 ELSE 0 END";

            using var command = new SqlCommand(validationSql, connection);
            using var reader = await command.ExecuteReaderAsync(cancellationToken);

            var missingTables = new List<string>();
            while (await reader.ReadAsync())
            {
                var tableName = reader.GetString(reader.GetOrdinal("TableName"));
                var exists = reader.GetInt32(reader.GetOrdinal("Exists")) == 1;

                if (!exists)
                {
                    missingTables.Add(tableName);
                }
            }

            if (missingTables.Any())
            {
                result.IsValid = false;
                result.ValidationMessages.AddRange(missingTables.Select(t => $"Missing required table: Staging.{t}"));
            }
            else
            {
                result.IsValid = true;
                result.ValidationMessages.Add("All required staging tables are present");
            }

            // Check for critical indexes (simplified check)
            if (result.IsValid)
            {
                result.ValidationMessages.Add("Staging schema validation passed");
                result.OptimizationRecommendations.Add("Consider running OPTIMIZE STAGING PERFORMANCE after large data loads");
            }

            _logger.LogInformation("Staging schema validation completed: {IsValid}", result.IsValid);
        }
        catch (Exception ex)
        {
            result.IsValid = false;
            result.ValidationMessages.Add($"Validation failed: {ex.Message}");
            _logger.LogError(ex, "Staging schema validation failed");
        }

        return result;
    }

    public async Task CleanupStagingDataAsync(
        Guid? sessionId = null,
        bool includeOrphanedSessions = false,
        CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            string cleanupSql;

            if (sessionId.HasValue)
            {
                // Clean up specific session
                cleanupSql = @"
                    DELETE FROM [Staging].[AccessPermissionStaging] WHERE [SessionId] = @SessionId
                    DELETE FROM [Staging].[CardStaging] WHERE [SessionId] = @SessionId
                    DELETE FROM [Staging].[VehicleStaging] WHERE [SessionId] = @SessionId
                    DELETE FROM [Staging].[DriverStaging] WHERE [SessionId] = @SessionId
                    DELETE FROM [Staging].[SeederSession] WHERE [Id] = @SessionId";
            }
            else if (includeOrphanedSessions)
            {
                // Clean up orphaned sessions (older than 24 hours and not running)
                cleanupSql = @"
                    DECLARE @OrphanedSessions TABLE (SessionId UNIQUEIDENTIFIER)
                    
                    INSERT INTO @OrphanedSessions
                    SELECT [Id] FROM [Staging].[SeederSession] 
                    WHERE [StartTime] < DATEADD(hour, -24, GETUTCDATE())
                    AND [Status] NOT IN ('Running', 'Processing')
                    
                    DELETE aps FROM [Staging].[AccessPermissionStaging] aps
                    INNER JOIN @OrphanedSessions os ON aps.[SessionId] = os.SessionId
                    
                    DELETE cs FROM [Staging].[CardStaging] cs
                    INNER JOIN @OrphanedSessions os ON cs.[SessionId] = os.SessionId
                    
                    DELETE vs FROM [Staging].[VehicleStaging] vs
                    INNER JOIN @OrphanedSessions os ON vs.[SessionId] = os.SessionId
                    
                    DELETE ds FROM [Staging].[DriverStaging] ds
                    INNER JOIN @OrphanedSessions os ON ds.[SessionId] = os.SessionId
                    
                    DELETE ss FROM [Staging].[SeederSession] ss
                    INNER JOIN @OrphanedSessions os ON ss.[Id] = os.SessionId";
            }
            else
            {
                // Clean up completed sessions only
                cleanupSql = @"
                    DECLARE @CompletedSessions TABLE (SessionId UNIQUEIDENTIFIER)
                    
                    INSERT INTO @CompletedSessions
                    SELECT [Id] FROM [Staging].[SeederSession] 
                    WHERE [Status] IN ('Completed', 'Failed', 'Cancelled')
                    AND [EndTime] < DATEADD(hour, -1, GETUTCDATE())
                    
                    DELETE aps FROM [Staging].[AccessPermissionStaging] aps
                    INNER JOIN @CompletedSessions cs ON aps.[SessionId] = cs.SessionId
                    
                    DELETE cst FROM [Staging].[CardStaging] cst
                    INNER JOIN @CompletedSessions cs ON cst.[SessionId] = cs.SessionId
                    
                    DELETE vs FROM [Staging].[VehicleStaging] vs
                    INNER JOIN @CompletedSessions cs ON vs.[SessionId] = cs.SessionId
                    
                    DELETE ds FROM [Staging].[DriverStaging] ds
                    INNER JOIN @CompletedSessions cs ON ds.[SessionId] = cs.SessionId
                    
                    DELETE ss FROM [Staging].[SeederSession] ss
                    INNER JOIN @CompletedSessions cs ON ss.[Id] = cs.SessionId";
            }

            using var command = new SqlCommand(cleanupSql, connection);
            if (sessionId.HasValue)
            {
                command.Parameters.AddWithValue("@SessionId", sessionId.Value);
            }

            var rowsAffected = await command.ExecuteNonQueryAsync(cancellationToken);

            _logger.LogInformation("Staging data cleanup completed: {RowsAffected} rows affected", rowsAffected);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cleanup staging data");
            throw;
        }
    }

    public async Task<SessionMetrics> GetSessionMetricsAsync(Guid sessionId, CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            const string sql = @"
                SELECT * FROM [Staging].[SeederSession] WHERE [Id] = @SessionId";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@SessionId", sessionId);

            using var reader = await command.ExecuteReaderAsync(cancellationToken);
            if (await reader.ReadAsync())
            {
                var metrics = new SessionMetrics
                {
                    SessionId = reader.GetGuid(reader.GetOrdinal("Id")),
                    SessionName = reader.GetString(reader.GetOrdinal("SessionName")),
                    Status = reader.GetString(reader.GetOrdinal("Status")),
                    Environment = reader.GetString(reader.GetOrdinal("Environment")),
                    CreatedBy = reader.GetString(reader.GetOrdinal("CreatedBy")),
                    StartTime = reader.GetDateTime(reader.GetOrdinal("StartTime")),
                    EndTime = reader.IsDBNull(reader.GetOrdinal("EndTime")) ? null : reader.GetDateTime(reader.GetOrdinal("EndTime")),
                    CurrentOperation = reader.IsDBNull(reader.GetOrdinal("CurrentOperation")) ? null : reader.GetString(reader.GetOrdinal("CurrentOperation")),
                    ProgressPercentage = reader.IsDBNull(reader.GetOrdinal("ProgressPercentage")) ? 0 : reader.GetDecimal(reader.GetOrdinal("ProgressPercentage")),
                    EstimatedCompletionTime = reader.IsDBNull(reader.GetOrdinal("EstimatedCompletionTime")) ? null : reader.GetDateTime(reader.GetOrdinal("EstimatedCompletionTime")),
                    ThroughputRecordsPerSecond = reader.IsDBNull(reader.GetOrdinal("ThroughputRecordsPerSecond")) ? null : reader.GetDecimal(reader.GetOrdinal("ThroughputRecordsPerSecond")),
                    TotalRows = reader.GetInt32(reader.GetOrdinal("TotalRows")),
                    SuccessfulRows = reader.GetInt32(reader.GetOrdinal("SuccessfulRows")),
                    FailedRows = reader.GetInt32(reader.GetOrdinal("FailedRows")),
                    ProcessedDrivers = reader.GetInt32(reader.GetOrdinal("ProcessedDrivers")),
                    ProcessedVehicles = reader.GetInt32(reader.GetOrdinal("ProcessedVehicles")),
                    ProcessedCards = reader.GetInt32(reader.GetOrdinal("ProcessedCards")),
                    ProcessedAccessRecords = reader.GetInt32(reader.GetOrdinal("ProcessedAccessRecords")),
                    ApiCallsTotal = reader.GetInt32(reader.GetOrdinal("ApiCallsTotal")),
                    ApiCallsSuccessful = reader.GetInt32(reader.GetOrdinal("ApiCallsSuccessful")),
                    ApiCallsFailed = reader.GetInt32(reader.GetOrdinal("ApiCallsFailed")),
                    ApiResponseTimeMs = reader.IsDBNull(reader.GetOrdinal("ApiResponseTimeMs")) ? null : reader.GetDouble(reader.GetOrdinal("ApiResponseTimeMs")),
                    SqlOperationsTotal = reader.GetInt32(reader.GetOrdinal("SqlOperationsTotal")),
                    SqlOperationsSuccessful = reader.GetInt32(reader.GetOrdinal("SqlOperationsSuccessful")),
                    SqlOperationsFailed = reader.GetInt32(reader.GetOrdinal("SqlOperationsFailed")),
                    BulkInsertTimeMs = reader.IsDBNull(reader.GetOrdinal("BulkInsertTimeMs")) ? null : reader.GetDouble(reader.GetOrdinal("BulkInsertTimeMs")),
                    ValidationTimeMs = reader.IsDBNull(reader.GetOrdinal("ValidationTimeMs")) ? null : reader.GetDouble(reader.GetOrdinal("ValidationTimeMs")),
                    ProcessingTimeMs = reader.IsDBNull(reader.GetOrdinal("ProcessingTimeMs")) ? null : reader.GetDouble(reader.GetOrdinal("ProcessingTimeMs")),
                    ValidationErrors = reader.GetInt32(reader.GetOrdinal("ValidationErrors")),
                    BusinessRuleViolations = reader.GetInt32(reader.GetOrdinal("BusinessRuleViolations")),
                    ReferentialIntegrityErrors = reader.GetInt32(reader.GetOrdinal("ReferentialIntegrityErrors")),
                    PeakMemoryUsageMB = reader.IsDBNull(reader.GetOrdinal("PeakMemoryUsageMB")) ? null : reader.GetDecimal(reader.GetOrdinal("PeakMemoryUsageMB")),
                    CacheHitRatio = reader.IsDBNull(reader.GetOrdinal("CacheHitRatio")) ? null : reader.GetDecimal(reader.GetOrdinal("CacheHitRatio")),
                    ConnectionPoolUsage = reader.IsDBNull(reader.GetOrdinal("ConnectionPoolUsage")) ? null : reader.GetInt32(reader.GetOrdinal("ConnectionPoolUsage")),
                    DataSourceInfo = reader.IsDBNull(reader.GetOrdinal("DataSourceInfo")) ? null : reader.GetString(reader.GetOrdinal("DataSourceInfo")),
                    ConfigurationSnapshot = reader.IsDBNull(reader.GetOrdinal("ConfigurationSnapshot")) ? null : reader.GetString(reader.GetOrdinal("ConfigurationSnapshot"))
                };

                if (metrics.EndTime.HasValue)
                {
                    metrics.Duration = metrics.EndTime.Value - metrics.StartTime;
                }

                // Parse error and warning logs
                var errorLog = reader.IsDBNull(reader.GetOrdinal("ErrorLog")) ? null : reader.GetString(reader.GetOrdinal("ErrorLog"));
                var warningsLog = reader.IsDBNull(reader.GetOrdinal("WarningsLog")) ? null : reader.GetString(reader.GetOrdinal("WarningsLog"));

                if (!string.IsNullOrEmpty(errorLog))
                {
                    try
                    {
                        metrics.Errors = JsonSerializer.Deserialize<List<string>>(errorLog) ?? new List<string>();
                    }
                    catch
                    {
                        metrics.Errors = new List<string> { errorLog };
                    }
                }

                if (!string.IsNullOrEmpty(warningsLog))
                {
                    try
                    {
                        metrics.Warnings = JsonSerializer.Deserialize<List<string>>(warningsLog) ?? new List<string>();
                    }
                    catch
                    {
                        metrics.Warnings = new List<string> { warningsLog };
                    }
                }

                return metrics;
            }

            throw new InvalidOperationException($"Session {sessionId} not found");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get session metrics for {SessionId}", sessionId);
            throw;
        }
    }

    public async Task<IEnumerable<SessionSummary>> GetActiveSessionsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            const string sql = @"
                SELECT * FROM [Staging].[vw_SessionProgressSummary]
                WHERE [Status] IN ('Created', 'Running', 'Processing')
                ORDER BY [StartTime] DESC";

            using var command = new SqlCommand(sql, connection);
            using var reader = await command.ExecuteReaderAsync(cancellationToken);

            var sessions = new List<SessionSummary>();
            while (await reader.ReadAsync())
            {
                sessions.Add(new SessionSummary
                {
                    SessionId = reader.GetGuid(reader.GetOrdinal("SessionId")),
                    SessionName = reader.GetString(reader.GetOrdinal("SessionName")),
                    Status = reader.GetString(reader.GetOrdinal("Status")),
                    CreatedBy = reader.GetString(reader.GetOrdinal("CreatedBy")),
                    StartTime = reader.GetDateTime(reader.GetOrdinal("StartTime")),
                    ProgressPercentage = reader.IsDBNull(reader.GetOrdinal("ProgressPercentage")) ? 0 : reader.GetDecimal(reader.GetOrdinal("ProgressPercentage")),
                    CurrentOperation = reader.IsDBNull(reader.GetOrdinal("CurrentOperation")) ? null : reader.GetString(reader.GetOrdinal("CurrentOperation")),
                    TotalRecords = reader.GetInt32(reader.GetOrdinal("TotalRecords")),
                    CompletedRecords = reader.GetInt32(reader.GetOrdinal("TotalCompleted")),
                    FailedRecords = 0, // Would need to be calculated from detailed staging tables
                    EstimatedCompletionTime = reader.IsDBNull(reader.GetOrdinal("EstimatedCompletionTime")) ? null : reader.GetDateTime(reader.GetOrdinal("EstimatedCompletionTime"))
                });
            }

            return sessions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get active sessions");
            throw;
        }
    }

    public async Task OptimizeStagingPerformanceAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            const string optimizationSql = @"
                -- Update statistics for optimal query plans
                UPDATE STATISTICS [Staging].[SeederSession] WITH FULLSCAN
                UPDATE STATISTICS [Staging].[DriverStaging] WITH FULLSCAN  
                UPDATE STATISTICS [Staging].[VehicleStaging] WITH FULLSCAN
                UPDATE STATISTICS [Staging].[CardStaging] WITH FULLSCAN
                UPDATE STATISTICS [Staging].[AccessPermissionStaging] WITH FULLSCAN
                UPDATE STATISTICS [Staging].[CustomerCache] WITH FULLSCAN
                UPDATE STATISTICS [Staging].[SiteCache] WITH FULLSCAN
                UPDATE STATISTICS [Staging].[DepartmentCache] WITH FULLSCAN
                UPDATE STATISTICS [Staging].[ModelCache] WITH FULLSCAN
                UPDATE STATISTICS [Staging].[ModuleCache] WITH FULLSCAN
                
                -- Clear procedure cache for recompilation with new statistics
                DBCC FREEPROCCACHE";

            using var command = new SqlCommand(optimizationSql, connection);
            command.CommandTimeout = _options.CommandTimeout * 2; // Allow extra time for optimization
            await command.ExecuteNonQueryAsync(cancellationToken);

            _logger.LogInformation("Staging performance optimization completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to optimize staging performance");
            throw;
        }
    }
}
