using XQ360.DataMigration.Web.Models;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Service interface for bulk seeding operations
/// </summary>
public interface IBulkSeederService
{
    /// <summary>
    /// Executes the bulk seeding operation
    /// </summary>
    /// <param name="options">Seeding options and parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Seeding result summary</returns>
    Task<SeederResult> ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken = default);

    /// <summary>
    /// Executes the bulk seeding operation with a specific session ID for SignalR messaging
    /// </summary>
    /// <param name="options">Seeding options and parameters</param>
    /// <param name="sessionId">Session ID for SignalR group messaging</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Seeding result summary</returns>
    Task<SeederResult> ExecuteSeederAsync(SeederOptions options, string? sessionId, CancellationToken cancellationToken = default);
}
